import { EventEmitter } from 'events';
import type { Session, SessionContext, ConversationMessage, ToolResult } from '../types';
export declare class SessionManager extends EventEmitter {
    private sessionsDir;
    constructor();
    initialize(): Promise<void>;
    private getSessionPath;
    private readSessionFile;
    private writeSessionFile;
    createSession(options: {
        workingDirectory: string;
        context: SessionContext;
    }): Promise<Session>;
    loadSession(sessionId: string): Promise<Session>;
    saveSession(session: Session): Promise<void>;
    deleteSession(sessionId: string): Promise<void>;
    getSessions(limit?: number): Promise<Session[]>;
    addMessage(sessionId: string, message: ConversationMessage): Promise<void>;
    getMessages(sessionId: string, limit?: number): Promise<ConversationMessage[]>;
    /**
     * Get messages with token-aware limiting
     */
    getMessagesWithTokenLimit(sessionId: string, model: string, maxTokens?: number, messageLimit?: number): Promise<ConversationMessage[]>;
    updateToolResult(sessionId: string, result: ToolResult): Promise<void>;
    searchSessions(query: string): Promise<Session[]>;
    cleanup(olderThanDays?: number): Promise<number>;
    close(): Promise<void>;
}
//# sourceMappingURL=session.d.ts.map