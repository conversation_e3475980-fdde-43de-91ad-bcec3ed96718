"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProviderManager = void 0;
const openai_1 = __importDefault(require("openai"));
const nanoid_1 = require("nanoid");
const logger_1 = require("../utils/logger");
const retry_1 = require("../utils/retry");
const tokenCounter_1 = require("../utils/tokenCounter");
class AIProviderManager {
    client;
    config;
    constructor(config) {
        this.config = {
            provider: config.provider,
            model: config.model,
            apiKey: config.apiKey,
            baseUrl: config.baseUrl,
            maxTokens: config.maxTokens || 4096,
            temperature: config.temperature || 0.7,
        };
        this.client = this.createClient();
    }
    createClient() {
        const clientConfig = {
            apiKey: this.config.apiKey || process.env['OPENAI_API_KEY'],
        };
        // Configure for different providers
        switch (this.config.provider) {
            case 'openai':
                // Default OpenAI configuration
                break;
            case 'deepseek':
                clientConfig.baseURL = this.config.baseUrl || 'https://api.deepseek.com/v1';
                clientConfig.apiKey = this.config.apiKey || process.env['DEEPSEEK_API_KEY'];
                break;
            case 'ollama':
                clientConfig.baseURL = this.config.baseUrl || 'http://localhost:11434/v1';
                clientConfig.apiKey = 'ollama'; // Ollama doesn't require a real API key
                break;
            case 'azure':
                clientConfig.baseURL = this.config.baseUrl;
                clientConfig.apiKey = this.config.apiKey || process.env['AZURE_OPENAI_API_KEY'];
                clientConfig.defaultHeaders = {
                    'api-key': clientConfig.apiKey,
                };
                break;
            default:
                throw new Error(`Unsupported AI provider: ${this.config.provider}`);
        }
        return new openai_1.default(clientConfig);
    }
    async updateConfig(newConfig) {
        this.config = {
            provider: newConfig.provider,
            model: newConfig.model,
            apiKey: newConfig.apiKey,
            baseUrl: newConfig.baseUrl,
            maxTokens: newConfig.maxTokens || this.config.maxTokens,
            temperature: newConfig.temperature || this.config.temperature,
        };
        this.client = this.createClient();
        logger_1.logger.info('AI provider config updated', {
            provider: this.config.provider,
            model: this.config.model
        });
    }
    async streamCompletion(options) {
        const { messages, tools, context, onChunk, onToolCall } = options;
        // Prepare system message with context
        const systemMessage = this.buildSystemMessage(context);
        const systemTokens = tokenCounter_1.TokenCounter.countMessageTokens(systemMessage);
        // Prepare tools for OpenAI format
        const openaiTools = this.convertTools(tools);
        const toolsTokens = tokenCounter_1.TokenCounter.countTokens(JSON.stringify(openaiTools));
        // Get model token limit and calculate available tokens
        const modelLimit = this.config.maxContextTokens || tokenCounter_1.TokenCounter.getModelTokenLimit(this.config.model);
        const maxCompletionTokens = this.config.maxTokens || 4096;
        // Truncate messages to fit within context window
        const truncatedMessages = tokenCounter_1.TokenCounter.truncateMessages(messages, modelLimit, systemTokens, toolsTokens + maxCompletionTokens);
        // Log token usage for debugging
        const messageTokens = tokenCounter_1.TokenCounter.countMessagesTokens(truncatedMessages);
        const totalInputTokens = systemTokens + messageTokens + toolsTokens;
        logger_1.logger.debug('Token usage', {
            model: this.config.model,
            modelLimit,
            systemTokens,
            messageTokens,
            toolsTokens,
            totalInputTokens,
            maxCompletionTokens,
            messagesCount: messages.length,
            truncatedCount: truncatedMessages.length
        });
        // Check if we're still over the limit
        if (totalInputTokens + maxCompletionTokens > modelLimit) {
            // Try to reduce further by using only the most recent messages
            const emergencyMessages = truncatedMessages.slice(-3); // Keep only last 3 messages
            const emergencyTokens = tokenCounter_1.TokenCounter.countMessagesTokens(emergencyMessages);
            const emergencyTotal = systemTokens + emergencyTokens + toolsTokens;
            if (emergencyTotal + maxCompletionTokens <= modelLimit) {
                logger_1.logger.warn('Using emergency token reduction', {
                    originalMessages: truncatedMessages.length,
                    emergencyMessages: emergencyMessages.length,
                    emergencyTotal,
                    modelLimit
                });
                // Use emergency reduced messages and process directly
                const emergencyOpenaiMessages = this.convertMessages([systemMessage, ...emergencyMessages]);
                return this.processStream(emergencyOpenaiMessages, openaiTools, onChunk, onToolCall);
            }
            else {
                const error = new Error(`Context too large even after emergency reduction: ${emergencyTotal} + ${maxCompletionTokens} > ${modelLimit} tokens`);
                logger_1.logger.error('Context window exceeded after emergency reduction', {
                    totalInputTokens,
                    emergencyTotal,
                    maxCompletionTokens,
                    modelLimit,
                    model: this.config.model
                });
                throw error;
            }
        }
        // Convert messages to OpenAI format
        const openaiMessages = this.convertMessages([systemMessage, ...truncatedMessages]);
        return this.processStream(openaiMessages, openaiTools, onChunk, onToolCall);
    }
    async processStream(openaiMessages, openaiTools, onChunk, onToolCall) {
        const result = await retry_1.RetryManager.retry(async () => {
            const stream = await this.client.chat.completions.create({
                model: this.config.model,
                messages: openaiMessages,
                tools: openaiTools.length > 0 ? openaiTools : undefined,
                tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
                max_tokens: this.config.maxTokens || null,
                temperature: this.config.temperature || null,
                stream: true,
            });
            return stream;
        }, {
            maxAttempts: 3,
            baseDelay: 1000,
            exponentialBackoff: true,
            retryCondition: (error) => retry_1.RetryManager.isRetryableError(error),
            onRetry: (error, attempt) => {
                logger_1.logger.warn('Retrying AI completion', {
                    attempt,
                    error: error.message,
                    provider: this.config.provider
                });
                onChunk?.({
                    type: 'error',
                    content: `Retrying request (attempt ${attempt})...`,
                    metadata: { error: error.message },
                });
            },
        });
        if (!result.success || !result.result) {
            throw result.error || new Error('Failed to get AI response');
        }
        const stream = result.result;
        try {
            let assistantMessage = '';
            const toolCalls = [];
            let currentToolCall = null;
            let argumentsBuffer = '';
            for await (const chunk of stream) {
                const delta = chunk.choices[0]?.delta;
                if (delta?.content) {
                    assistantMessage += delta.content;
                    onChunk?.({
                        type: 'text',
                        content: delta.content,
                    });
                }
                if (delta?.tool_calls) {
                    for (const toolCallDelta of delta.tool_calls) {
                        const index = toolCallDelta.index;
                        // Process tool call delta
                        // Handle tool call by index - each index represents a different tool call
                        if (index !== undefined) {
                            // If this is a new tool call (different index or first time)
                            if (!currentToolCall || index !== currentToolCall.index) {
                                // Finish previous tool call if it exists and is complete
                                if (currentToolCall && this.isValidToolCall(currentToolCall)) {
                                    logger_1.logger.debug('Finishing previous tool call', {
                                        toolName: currentToolCall.name,
                                        arguments: currentToolCall.arguments
                                    });
                                    toolCalls.push(currentToolCall);
                                }
                                // Start new tool call
                                currentToolCall = {
                                    id: toolCallDelta.id || (0, nanoid_1.nanoid)(),
                                    name: toolCallDelta.function?.name || '',
                                    arguments: {},
                                    riskLevel: 'safe',
                                };
                                currentToolCall.index = index;
                                argumentsBuffer = '';
                                // Tool call started
                            }
                            // Update tool call name if provided
                            if (toolCallDelta.function?.name && currentToolCall) {
                                currentToolCall.name = toolCallDelta.function.name;
                            }
                            // Accumulate arguments if provided
                            if (toolCallDelta.function?.arguments && currentToolCall) {
                                argumentsBuffer += toolCallDelta.function.arguments;
                                try {
                                    // Try to parse the accumulated arguments
                                    const args = JSON.parse(argumentsBuffer);
                                    currentToolCall.arguments = args;
                                }
                                catch (error) {
                                    // Partial JSON, continue accumulating
                                    // This is normal for streaming responses
                                }
                            }
                        }
                    }
                }
                if (chunk.choices[0]?.finish_reason === 'tool_calls') {
                    // Finish the last tool call if it's valid
                    if (currentToolCall && this.isValidToolCall(currentToolCall)) {
                        toolCalls.push(currentToolCall);
                    }
                    currentToolCall = null;
                    argumentsBuffer = '';
                }
            }
            // Handle any remaining tool call that wasn't finished by finish_reason
            if (currentToolCall && this.isValidToolCall(currentToolCall)) {
                toolCalls.push(currentToolCall);
            }
            // Create the assistant message with tool calls
            const assistantMessageWithTools = {
                id: (0, nanoid_1.nanoid)(),
                role: 'assistant',
                content: assistantMessage || '',
                timestamp: new Date(),
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
            };
            // Execute tool calls if any and return the assistant message
            // The tool results will be handled separately in the conversation flow
            if (toolCalls.length > 0 && onToolCall) {
                const toolResults = [];
                for (const toolCall of toolCalls) {
                    onChunk?.({
                        type: 'tool_call',
                        content: `Executing ${toolCall.name}...`,
                        metadata: { toolCall },
                    });
                    const result = await onToolCall(toolCall);
                    toolResults.push(result);
                    onChunk?.({
                        type: 'tool_result',
                        content: result.success ? result.output : `Error: ${result.error}`,
                        metadata: { result },
                    });
                }
                // Add tool results to the assistant message for reference
                assistantMessageWithTools.toolResults = toolResults;
            }
            return assistantMessageWithTools;
        }
        catch (error) {
            logger_1.logger.error('Error in AI completion', error);
            throw error;
        }
    }
    buildSystemMessage(context) {
        const { projectStructure } = context;
        // Build a more concise system message to save tokens
        const systemPrompt = `You are Kritrima, an AI CLI assistant with tool execution capabilities.

CONTEXT: ${projectStructure.root} (${projectStructure.type}${projectStructure.language ? `, ${projectStructure.language}` : ''})

TOOLS & USAGE:
- shell: Execute commands {"command": "cmd"}
- file_read: Read files {"path": "file"}
- file_write: Write files {"path": "file", "content": "text"}
- file_create: Create files {"path": "file", "content": "text"}
- file_delete: Delete files {"path": "file"}
- file_move: Move files {"source": "from", "destination": "to"}
- file_copy: Copy files {"source": "from", "destination": "to"}
- file_search: Search patterns {"pattern": "text"}
- grep: Search in files {"pattern": "text", "path": "file"}
- list_directory: List contents {"path": "dir"} or {}
- system_info: Get system info {}

RULES:
1. Always provide ALL required parameters
2. Use exact parameter names
3. Assess risk before destructive operations
4. Explain actions clearly

EXAMPLES:
- "show package.json" → file_read {"path": "package.json"}
- "list files" → list_directory {}
- "run npm install" → shell {"command": "npm install"}

Be helpful and safe.`;
        return {
            id: (0, nanoid_1.nanoid)(),
            role: 'system',
            content: systemPrompt,
            timestamp: new Date(),
        };
    }
    convertMessages(messages) {
        return messages.map(msg => this.convertMessage(msg));
    }
    convertMessage(message) {
        const converted = {
            role: message.role,
            content: message.content || '',
        };
        // Handle assistant messages with tool calls
        if (message.role === 'assistant' && message.toolCalls) {
            converted.tool_calls = message.toolCalls.map(tc => ({
                id: tc.id,
                type: 'function',
                function: {
                    name: tc.name,
                    arguments: JSON.stringify(tc.arguments),
                },
            }));
        }
        // Handle tool messages - these must have tool_call_id
        if (message.role === 'tool' && message.toolCallId) {
            converted.tool_call_id = message.toolCallId;
            // Ensure content is not empty for tool messages
            if (!converted.content) {
                converted.content = 'Tool execution completed';
            }
        }
        return converted;
    }
    convertTools(tools) {
        return tools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: tool.parameters,
            },
        }));
    }
    async testConnection() {
        try {
            const response = await this.client.chat.completions.create({
                model: this.config.model,
                messages: [{ role: 'user', content: 'Hello' }],
                max_tokens: 10,
            });
            return !!response.choices[0]?.message?.content;
        }
        catch (error) {
            logger_1.logger.error('AI provider connection test failed', error);
            return false;
        }
    }
    isValidToolCall(toolCall) {
        const isValid = !!(toolCall.id &&
            toolCall.name &&
            toolCall.name.trim() !== '' &&
            toolCall.arguments !== undefined &&
            toolCall.riskLevel);
        if (!isValid) {
            logger_1.logger.debug('AI Provider tool call validation failed', {
                hasId: !!toolCall.id,
                hasName: !!toolCall.name,
                nameNotEmpty: !!(toolCall.name && toolCall.name.trim() !== ''),
                hasArguments: toolCall.arguments !== undefined,
                hasRiskLevel: !!toolCall.riskLevel,
                toolCall
            });
        }
        return isValid;
    }
    getConfig() {
        return { ...this.config };
    }
}
exports.AIProviderManager = AIProviderManager;
//# sourceMappingURL=providers.js.map