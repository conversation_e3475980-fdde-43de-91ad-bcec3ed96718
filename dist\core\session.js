"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionManager = void 0;
const events_1 = require("events");
const nanoid_1 = require("nanoid");
const path_1 = __importDefault(require("path"));
const os_1 = __importDefault(require("os"));
const promises_1 = __importDefault(require("fs/promises"));
const logger_1 = require("../utils/logger");
const tokenCounter_1 = require("../utils/tokenCounter");
class SessionManager extends events_1.EventEmitter {
    sessionsDir;
    constructor() {
        super();
        this.sessionsDir = path_1.default.join(os_1.default.homedir(), '.kritrima', 'sessions');
    }
    async initialize() {
        try {
            // Ensure directory exists
            await promises_1.default.mkdir(this.sessionsDir, { recursive: true });
            logger_1.logger.info('Session manager initialized', { sessionsDir: this.sessionsDir });
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize session manager', error);
            throw error;
        }
    }
    getSessionPath(sessionId) {
        return path_1.default.join(this.sessionsDir, `${sessionId}.json`);
    }
    async readSessionFile(sessionId) {
        try {
            const sessionPath = this.getSessionPath(sessionId);
            const content = await promises_1.default.readFile(sessionPath, 'utf-8');
            const data = JSON.parse(content);
            // Convert date strings back to Date objects
            data.createdAt = new Date(data.createdAt);
            data.updatedAt = new Date(data.updatedAt);
            data.messages = data.messages.map((msg) => ({
                ...msg,
                timestamp: new Date(msg.timestamp),
            }));
            return data;
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                return null; // File doesn't exist
            }
            throw error;
        }
    }
    async writeSessionFile(session) {
        const sessionPath = this.getSessionPath(session.id);
        const content = JSON.stringify(session, null, 2);
        await promises_1.default.writeFile(sessionPath, content, 'utf-8');
    }
    async createSession(options) {
        const sessionId = (0, nanoid_1.nanoid)();
        const now = new Date();
        const metadata = {
            totalCommands: 0,
            successfulCommands: 0,
            failedCommands: 0,
            lastActivity: now,
            tags: [],
        };
        const session = {
            id: sessionId,
            createdAt: now,
            updatedAt: now,
            workingDirectory: options.workingDirectory,
            context: options.context,
            messages: [],
            metadata,
        };
        try {
            await this.writeSessionFile(session);
            logger_1.logger.info('Session created', { sessionId, workingDirectory: options.workingDirectory });
            this.emit('session-created', session);
            return session;
        }
        catch (error) {
            logger_1.logger.error('Failed to create session', error);
            throw error;
        }
    }
    async loadSession(sessionId) {
        try {
            const session = await this.readSessionFile(sessionId);
            if (!session) {
                throw new Error(`Session not found: ${sessionId}`);
            }
            logger_1.logger.info('Session loaded', { sessionId });
            return session;
        }
        catch (error) {
            logger_1.logger.error('Failed to load session', error);
            throw error;
        }
    }
    async saveSession(session) {
        try {
            session.updatedAt = new Date();
            await this.writeSessionFile(session);
            logger_1.logger.debug('Session saved', { sessionId: session.id });
        }
        catch (error) {
            logger_1.logger.error('Failed to save session', error);
            throw error;
        }
    }
    async deleteSession(sessionId) {
        try {
            const sessionPath = this.getSessionPath(sessionId);
            await promises_1.default.unlink(sessionPath);
            logger_1.logger.info('Session deleted', { sessionId });
            this.emit('session-deleted', { sessionId });
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                throw new Error(`Session not found: ${sessionId}`);
            }
            logger_1.logger.error('Failed to delete session', error);
            throw error;
        }
    }
    async getSessions(limit = 50) {
        try {
            const files = await promises_1.default.readdir(this.sessionsDir);
            const sessionFiles = files.filter(file => file.endsWith('.json'));
            const sessions = [];
            for (const file of sessionFiles.slice(0, limit)) {
                const sessionId = path_1.default.basename(file, '.json');
                try {
                    const session = await this.readSessionFile(sessionId);
                    if (session) {
                        // Don't load full context and messages for list view
                        sessions.push({
                            ...session,
                            context: {},
                            messages: [],
                        });
                    }
                }
                catch (error) {
                    // Skip corrupted session files
                    logger_1.logger.warn('Skipping corrupted session file', { file, error });
                }
            }
            // Sort by updated date
            sessions.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
            return sessions;
        }
        catch (error) {
            logger_1.logger.error('Failed to get sessions', error);
            throw error;
        }
    }
    async addMessage(sessionId, message) {
        try {
            const session = await this.loadSession(sessionId);
            session.messages.push(message);
            session.updatedAt = new Date();
            await this.saveSession(session);
            logger_1.logger.debug('Message added', { sessionId, messageId: message.id, role: message.role });
            this.emit('message-added', { sessionId, message });
        }
        catch (error) {
            logger_1.logger.error('Failed to add message', error);
            throw error;
        }
    }
    async getMessages(sessionId, limit = 20) {
        try {
            const session = await this.loadSession(sessionId);
            // Use a more conservative default limit to prevent token overflow
            // The AI provider will further truncate based on actual token counts
            const messages = session.messages.slice(-limit);
            logger_1.logger.debug('Retrieved messages', {
                sessionId,
                totalMessages: session.messages.length,
                returnedMessages: messages.length,
                limit
            });
            return messages;
        }
        catch (error) {
            logger_1.logger.error('Failed to get messages', error);
            throw error;
        }
    }
    /**
     * Get messages with token-aware limiting
     */
    async getMessagesWithTokenLimit(sessionId, model, maxTokens, messageLimit) {
        try {
            const session = await this.loadSession(sessionId);
            if (session.messages.length === 0) {
                return [];
            }
            // Calculate optimal message limit based on model
            const modelLimit = tokenCounter_1.TokenCounter.getModelTokenLimit(model);
            const targetLimit = maxTokens || Math.floor(modelLimit * 0.6); // Use 60% of model limit for messages
            // Apply message count limit first if specified
            const recentMessages = messageLimit
                ? session.messages.slice(-messageLimit)
                : session.messages;
            // Start with recent messages and work backwards
            const messages = [];
            let currentTokens = 0;
            for (let i = recentMessages.length - 1; i >= 0; i--) {
                const message = recentMessages[i];
                const messageTokens = tokenCounter_1.TokenCounter.countMessageTokens(message);
                if (currentTokens + messageTokens <= targetLimit) {
                    messages.unshift(message);
                    currentTokens += messageTokens;
                }
                else {
                    break;
                }
            }
            logger_1.logger.debug('Retrieved messages with token limit', {
                sessionId,
                model,
                totalMessages: session.messages.length,
                returnedMessages: messages.length,
                estimatedTokens: currentTokens,
                targetLimit
            });
            return messages;
        }
        catch (error) {
            logger_1.logger.error('Failed to get messages with token limit', error);
            throw error;
        }
    }
    async updateToolResult(sessionId, result) {
        try {
            const session = await this.loadSession(sessionId);
            // Update session metadata
            session.metadata.totalCommands++;
            if (result.success) {
                session.metadata.successfulCommands++;
            }
            else {
                session.metadata.failedCommands++;
            }
            session.metadata.lastActivity = new Date();
            await this.saveSession(session);
            logger_1.logger.debug('Tool result updated', { sessionId, resultId: result.id });
        }
        catch (error) {
            logger_1.logger.error('Failed to update tool result', error);
            throw error;
        }
    }
    async searchSessions(query) {
        try {
            const sessions = await this.getSessions();
            const lowerQuery = query.toLowerCase();
            return sessions.filter(session => {
                return session.workingDirectory.toLowerCase().includes(lowerQuery) ||
                    session.messages.some(msg => msg.content.toLowerCase().includes(lowerQuery));
            }).slice(0, 20);
        }
        catch (error) {
            logger_1.logger.error('Failed to search sessions', error);
            throw error;
        }
    }
    async cleanup(olderThanDays = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
            const files = await promises_1.default.readdir(this.sessionsDir);
            const sessionFiles = files.filter(file => file.endsWith('.json'));
            let deletedCount = 0;
            for (const file of sessionFiles) {
                const sessionId = path_1.default.basename(file, '.json');
                try {
                    const session = await this.readSessionFile(sessionId);
                    if (session && session.updatedAt < cutoffDate) {
                        await this.deleteSession(sessionId);
                        deletedCount++;
                    }
                }
                catch (error) {
                    // Skip corrupted files
                }
            }
            logger_1.logger.info('Session cleanup completed', {
                deletedSessions: deletedCount,
                olderThanDays
            });
            return deletedCount;
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup sessions', error);
            throw error;
        }
    }
    async close() {
        logger_1.logger.info('Session manager closed');
    }
}
exports.SessionManager = SessionManager;
//# sourceMappingURL=session.js.map